"use client";

import { Fragment, useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Menu, Transition } from '@headlessui/react';
import {
  BellIcon,
  CheckIcon,
  ArrowPathIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';
import { useNotifications } from '@/hooks/useNotifications';
import { cn } from '@/lib/utils';
import { NotificationBadge } from './NotificationBadge';
import { NotificationPreview } from './NotificationPreview';
import { usePreventScrollLock } from '@/hooks/usePreventScrollLock';

export function NotificationDropdown() {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
  } = useNotifications();

  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  const [isAnimating, setIsAnimating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const openStateRef = useRef(false);

  // Prevent scroll lock when dropdown is open
  usePreventScrollLock(isOpen);

  // Update isOpen state when menu state changes (using useEffect to avoid setState during render)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (openStateRef.current !== isOpen) {
        setIsOpen(openStateRef.current);
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [isOpen]);

  // Filter notifications based on selected filter
  const filteredNotifications = filter === 'unread'
    ? notifications.filter(n => !n.read)
    : notifications;

  // Show only the latest 8 notifications in dropdown
  const recentNotifications = filteredNotifications.slice(0, 8);

  // Handle refresh with animation
  const handleRefresh = async () => {
    setIsAnimating(true);
    await refreshNotifications();
    setTimeout(() => setIsAnimating(false), 1000);
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  return (
    <Menu as="div" className="relative">
      {({ open }) => {
        // Track open state in ref to avoid setState during render
        openStateRef.current = open;

        return (
          <>
      <Menu.Button className="flex items-center justify-center p-2 rounded-xl w-10 h-10 transition-all duration-200 text-gray-700 hover:text-blue-600 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-md relative group active:scale-95">
        {unreadCount > 0 ? (
          <BellSolidIcon className="h-6 w-6 text-blue-600 animate-bounce" />
        ) : (
          <BellIcon className="h-6 w-6 group-hover:scale-110 group-hover:rotate-12 transition-all duration-200" />
        )}
        <NotificationBadge
          count={unreadCount}
          animate={true}
          size="md"
          color="red"
        />
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="transform opacity-0 scale-95 translate-y-2"
        enterTo="transform opacity-100 scale-100 translate-y-0"
        leave="transition ease-in duration-150"
        leaveFrom="transform opacity-100 scale-100 translate-y-0"
        leaveTo="transform opacity-0 scale-95 translate-y-2"
      >
        <Menu.Items className="absolute right-0 mt-3 w-96 bg-white rounded-xl shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none z-50 max-h-[32rem] overflow-hidden border border-gray-100">
          {/* Enhanced Header */}
          <div className="px-4 py-4 bg-gradient-to-r from-blue-500 to-purple-600 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BellIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">
                    Notifications
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up! 🎉'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Refresh Button */}
                <button
                  onClick={handleRefresh}
                  className="p-2 rounded-xl bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm"
                  title="Refresh notifications"
                >
                  <ArrowPathIcon className={cn(
                    "h-5 w-5 text-white transition-transform duration-200",
                    isAnimating && "animate-spin"
                  )} />
                </button>

                {/* Settings Button */}
                <Link
                  href="/settings/notifications"
                  className="p-2 rounded-xl bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm"
                  title="Notification settings"
                >
                  <Cog6ToothIcon className="h-5 w-5 text-white" />
                </Link>
              </div>
            </div>

            {/* Filter Tabs */}
            <div className="flex space-x-1 bg-white/20 rounded-xl p-1 backdrop-blur-sm">
              <button
                onClick={() => setFilter('all')}
                className={cn(
                  "flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                  filter === 'all'
                    ? "bg-white text-blue-600 shadow-lg"
                    : "text-white hover:text-blue-100 hover:bg-white/20"
                )}
              >
                All ({notifications.length})
              </button>
              <button
                onClick={() => setFilter('unread')}
                className={cn(
                  "flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 relative",
                  filter === 'unread'
                    ? "bg-white text-blue-600 shadow-lg"
                    : "text-white hover:text-blue-100 hover:bg-white/20"
                )}
              >
                Unread
                {unreadCount > 0 && (
                  <span className="ml-1 inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-bold bg-red-500 text-white">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Quick Actions */}
            {unreadCount > 0 && (
              <div className="mt-3 flex justify-end">
                <button
                  onClick={handleMarkAllAsRead}
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-white rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <CheckIcon className="h-3 w-3 mr-1" />
                  Mark all read
                </button>
              </div>
            )}
          </div>

          {/* Enhanced Notifications List */}
          <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {isLoading && notifications.length === 0 ? (
              <div className="p-4">
                <div className="animate-pulse space-y-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex items-start space-x-3 p-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4" />
                        <div className="h-3 bg-gray-200 rounded w-1/2" />
                      </div>
                      <div className="w-2 h-2 bg-gray-200 rounded-full" />
                    </div>
                  ))}
                </div>
              </div>
            ) : recentNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BellIcon className="h-8 w-8 text-gray-400" />
                </div>
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}
                </h4>
                <p className="text-xs text-gray-500">
                  {filter === 'unread'
                    ? 'You\'re all caught up!'
                    : 'We\'ll notify you when something happens'}
                </p>
              </div>
            ) : (
              <div>
                {recentNotifications.map((notification, index) => (
                  <Menu.Item key={notification.id}>
                    {({ active }) => (
                      <div
                        style={{
                          animationDelay: `${index * 50}ms`,
                        }}
                      >
                        <NotificationPreview
                          notification={notification}
                          onMarkAsRead={markAsRead}
                          onDelete={deleteNotification}
                          compact={true}
                        />
                      </div>
                    )}
                  </Menu.Item>
                ))}
              </div>
            )}
          </div>

          {/* Enhanced Footer */}
          {notifications.length > 0 && (
            <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-blue-50 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  Showing {recentNotifications.length} of {filteredNotifications.length}
                </div>
                <Link
                  href="/notifications"
                  className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-colors"
                >
                  View all
                  <svg className="ml-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          )}
        </Menu.Items>
      </Transition>
          </>
        );
      }}
    </Menu>
  );
}
